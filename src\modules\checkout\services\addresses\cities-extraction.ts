import {GET} from '@/lib/http-methods';
import {City, CityResponseDataType} from '@/modules/checkout/types/addresses';
import {castToCityType} from '@/modules/checkout/utils/types-casting/addresses';

export async function retrieveCities(countryCode: string): Promise<City[]> {
  try {
    const res = await GET(`/addresses/cities/${countryCode}}`, {});
    return (res.data as CityResponseDataType[]).map((city) =>
      castToCityType(city),
    );
  } catch (error) {
    return [];
  }
}
