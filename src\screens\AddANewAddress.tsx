import React from 'react';
import {View, Image, Text} from 'react-native';
import {KeyboardAwareScrollView} from 'react-native-keyboard-aware-scroll-view';

import {components} from '../components';
import useAddressCreation from '../modules/checkout/hooks/addresses/use-address-creation';

const AddANewAddress: React.FC = (): JSX.Element => {
  const {
    createAddress,
    formData,
    handleInputChange,
    isLoading,
    warning,
    wrongInputs,
  } = useAddressCreation();

  const renderStatusBar = () => {
    return <components.StatusBar />;
  };

  const renderHeader = () => {
    return <components.Header title='Add a new address' goBack={true} />;
  };

  const renderMap = () => {
    return (
      <View
        style={{
          marginTop: 10,
          flex: 1,
          paddingLeft: 20,
        }}
      >
        <Image
          source={{uri: 'https://george-fx.github.io/kastelli/map/01.jpg'}}
          style={{flex: 1}}
          resizeMode='contain'
        />
      </View>
    );
  };

  const renderContent = () => {
    return (
      <KeyboardAwareScrollView
        contentContainerStyle={{
          paddingHorizontal: 20,
          paddingTop: 40,
          paddingBottom: 20,
        }}
        enableOnAndroid={true}
        showsVerticalScrollIndicator={false}
        style={{flexGrow: 0}}
      >
        {warning && (
          <View style={{marginBottom: 20}}>
            <Text style={{color: '#E82837', textAlign: 'center', fontSize: 14}}>
              {warning}
            </Text>
          </View>
        )}
        <components.InputField
          label='Address Type'
          placeholder='Home'
          value={formData.type}
          onChangeText={(value) => handleInputChange('type', value)}
          containerStyle={{
            marginBottom: 22,
          }}
        />
        <components.InputField
          label='Address'
          placeholder='Enter your address'
          value={formData.address}
          onChangeText={(value) => handleInputChange('address', value)}
          containerStyle={{
            marginBottom: 22,
          }}
        />
        <components.InputField
          label='City'
          placeholder='Enter city'
          value={formData.city}
          onChangeText={(value) => handleInputChange('city', value)}
          containerStyle={{
            marginBottom: 22,
          }}
        />
        <components.InputField
          label='State'
          placeholder='Enter state'
          value={formData.state}
          onChangeText={(value) => handleInputChange('state', value)}
          containerStyle={{
            marginBottom: 22,
          }}
        />
        <components.InputField
          label='Zip Code'
          placeholder='Enter zip code'
          value={formData.zipCode}
          onChangeText={(value) => handleInputChange('zipCode', value)}
          containerStyle={{
            marginBottom: 22,
          }}
        />
        <components.InputField
          label='Country'
          placeholder='Enter country'
          value={formData.country}
          onChangeText={(value) => handleInputChange('country', value)}
          containerStyle={{
            marginBottom: 22,
          }}
        />
      </KeyboardAwareScrollView>
    );
  };

  const renderButton = () => {
    return (
      <components.Button
        title={isLoading ? 'Saving...' : 'Save Address'}
        onPress={createAddress}
        containerStyle={{
          margin: 20,
        }}
      />
    );
  };

  const renderHomeIndicator = () => {
    return <components.HomeIndicator />;
  };

  return (
    <components.SmartView>
      {renderStatusBar()}
      {renderHeader()}
      {renderMap()}
      {renderContent()}
      {renderButton()}
      {renderHomeIndicator()}
    </components.SmartView>
  );
};

export default AddANewAddress;
