import React from 'react';
import {View, Image, Text} from 'react-native';
import {KeyboardAwareScrollView} from 'react-native-keyboard-aware-scroll-view';

import {components} from '../components';
import useAddressCreation from '../modules/checkout/hooks/addresses/use-address-creation';

const AddANewAddress: React.FC = (): JSX.Element => {
  const {
    createAddress,
    formData,
    handleInputChange,
    isLoading,
    warning,
    wrongInputs,
  } = useAddressCreation();

  const renderStatusBar = () => {
    return <components.StatusBar />;
  };

  const renderHeader = () => {
    return <components.Header title='Add a new address' goBack={true} />;
  };

  const renderMap = () => {
    return (
      <View
        style={{
          marginTop: 10,
          flex: 1,
          paddingLeft: 20,
        }}
      >
        <Image
          source={{uri: 'https://george-fx.github.io/kastelli/map/01.jpg'}}
          style={{flex: 1}}
          resizeMode='contain'
        />
      </View>
    );
  };

  const renderContent = () => {
    return (
      <KeyboardAwareScrollView
        contentContainerStyle={{
          paddingHorizontal: 20,
          paddingTop: 40,
          paddingBottom: 20,
        }}
        enableOnAndroid={true}
        showsVerticalScrollIndicator={false}
        style={{flexGrow: 0}}
      >
        {warning && (
          <View style={{marginBottom: 20}}>
            <Text style={{color: '#E82837', textAlign: 'center', fontSize: 14}}>
              {warning}
            </Text>
          </View>
        )}
        <components.InputField
          label='Email'
          placeholder='Enter your email'
          value={formData.email}
          onChangeText={(value) => handleInputChange('email', value)}
          keyboardType='email-address'
          containerStyle={{
            marginBottom: 22,
            borderColor: wrongInputs.includes('email') ? '#E82837' : '#DBE9F5',
          }}
        />
        <components.InputField
          label='First Name'
          placeholder='Enter your first name'
          value={formData.firstName}
          onChangeText={(value) => handleInputChange('firstName', value)}
          containerStyle={{
            marginBottom: 22,
            borderColor: wrongInputs.includes('firstName')
              ? '#E82837'
              : '#DBE9F5',
          }}
        />
        <components.InputField
          label='Last Name'
          placeholder='Enter your last name'
          value={formData.lastName}
          onChangeText={(value) => handleInputChange('lastName', value)}
          containerStyle={{
            marginBottom: 22,
            borderColor: wrongInputs.includes('lastName')
              ? '#E82837'
              : '#DBE9F5',
          }}
        />
        <components.InputField
          label='Address Line 1'
          placeholder='Enter your address'
          value={formData.address1}
          onChangeText={(value) => handleInputChange('address1', value)}
          containerStyle={{
            marginBottom: 22,
            borderColor: wrongInputs.includes('address1')
              ? '#E82837'
              : '#DBE9F5',
          }}
        />
        <components.InputField
          label='Address Line 2 (Optional)'
          placeholder='Apartment, suite, etc.'
          value={formData.address2}
          onChangeText={(value) => handleInputChange('address2', value)}
          containerStyle={{
            marginBottom: 22,
          }}
        />
        <components.InputField
          label='Company (Optional)'
          placeholder='Enter company name'
          value={formData.company}
          onChangeText={(value) => handleInputChange('company', value)}
          containerStyle={{
            marginBottom: 22,
          }}
        />
        <components.InputField
          label='City'
          placeholder='Enter city'
          value={formData.city}
          onChangeText={(value) => handleInputChange('city', value)}
          containerStyle={{
            marginBottom: 22,
            borderColor: wrongInputs.includes('city') ? '#E82837' : '#DBE9F5',
          }}
        />
        <components.InputField
          label='State/Province'
          placeholder='Enter state or province'
          value={formData.province}
          onChangeText={(value) => handleInputChange('province', value)}
          containerStyle={{
            marginBottom: 22,
            borderColor: wrongInputs.includes('province')
              ? '#E82837'
              : '#DBE9F5',
          }}
        />
        <components.InputField
          label='Zip/Postal Code'
          placeholder='Enter zip or postal code'
          value={formData.zip}
          onChangeText={(value) => handleInputChange('zip', value)}
          containerStyle={{
            marginBottom: 22,
            borderColor: wrongInputs.includes('zip') ? '#E82837' : '#DBE9F5',
          }}
        />
        <components.PhoneNumberInput
          label='Phone Number'
          placeholder='Enter phone number'
          value={formData.phone}
          onChangeText={(value) => handleInputChange('phone', value)}
          hasError={wrongInputs.includes('phone')}
          containerStyle={{
            marginBottom: 22,
          }}
        />
        <components.InputField
          label='Country'
          placeholder='Enter country'
          value={formData.country}
          onChangeText={(value) => handleInputChange('country', value)}
          containerStyle={{
            marginBottom: 22,
          }}
        />
      </KeyboardAwareScrollView>
    );
  };

  const renderButton = () => {
    return (
      <components.Button
        title={isLoading ? 'Saving...' : 'Save Address'}
        onPress={createAddress}
        containerStyle={{
          margin: 20,
        }}
      />
    );
  };

  const renderHomeIndicator = () => {
    return <components.HomeIndicator />;
  };

  return (
    <components.SmartView>
      {renderStatusBar()}
      {renderHeader()}
      {renderMap()}
      {renderContent()}
      {renderButton()}
      {renderHomeIndicator()}
    </components.SmartView>
  );
};

export default AddANewAddress;
