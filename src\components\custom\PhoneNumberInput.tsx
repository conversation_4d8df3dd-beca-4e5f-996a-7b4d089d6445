import React, {useState, useRef} from 'react';
import {View, Text, StyleSheet} from 'react-native';
import PhoneInput from 'react-native-phone-input';
import {theme} from '../../constants';

interface Props {
  inputName?: string;
  code?: string;
  className?: string;
  primaryTheme?: boolean;
  containerStyle?: object;
  label?: string;
  placeholder?: string;
  onChangeText?: (text: string) => void;
  value?: string;
  hasError?: boolean;
}

const PhoneNumberInput: React.FC<Props> = ({
  inputName,
  code,
  primaryTheme = true,
  containerStyle,
  label,
  placeholder = 'Enter phone number',
  onChangeText,
  value,
  hasError = false,
}): JSX.Element => {
  const phoneRef = useRef<PhoneInput>(null);
  const [phoneNumber, setPhoneNumber] = useState(value || '');

  const handlePhoneChange = (number: string) => {
    setPhoneNumber(number);
    if (onChangeText) {
      // Extract just the number part without country code for consistency with web version
      const formattedNumber = phoneRef.current?.getValue() || number;
      onChangeText(formattedNumber.replace(/^\+\d+/, ''));
    }
  };

  return (
    <View style={[styles.container, containerStyle]}>
      <View
        style={[
          styles.inputContainer,
          hasError && styles.errorBorder,
          primaryTheme && !hasError && styles.primaryBorder,
          !primaryTheme && !hasError && styles.grayBorder,
        ]}
      >
        <PhoneInput
          ref={phoneRef}
          style={styles.phoneInput}
          initialCountry={code ? undefined : 'us'}
          initialValue={code ? `${code}${phoneNumber}` : phoneNumber}
          onChangePhoneNumber={handlePhoneChange}
          textStyle={styles.phoneText}
          flagStyle={styles.flag}
          textProps={{
            placeholder: placeholder,
            placeholderTextColor: '#A7AFB7',
          }}
        />
        
        {label && (
          <View style={styles.labelContainer}>
            <Text style={styles.labelText}>{label}</Text>
          </View>
        )}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    width: '100%',
  },
  inputContainer: {
    borderWidth: 1,
    borderRadius: 15,
    paddingVertical: 12,
    paddingHorizontal: 16,
    backgroundColor: theme.colors.white,
    minHeight: 50,
    justifyContent: 'center',
    position: 'relative',
  },
  primaryBorder: {
    borderColor: theme.colors.mainColor,
  },
  grayBorder: {
    borderColor: theme.colors.lightBlue,
  },
  errorBorder: {
    borderColor: '#E82837',
  },
  phoneInput: {
    flex: 1,
    height: '100%',
  },
  phoneText: {
    ...theme.fonts.DMSans_400Regular,
    fontSize: 16,
    color: theme.colors.mainColor,
  },
  flag: {
    width: 24,
    height: 16,
    marginRight: 8,
  },
  labelContainer: {
    position: 'absolute',
    top: -12,
    left: 10,
    paddingHorizontal: 10,
    backgroundColor: theme.colors.white,
  },
  labelText: {
    ...theme.fonts.DMSans_500Medium,
    fontSize: 12,
    textTransform: 'uppercase',
    color: theme.colors.textColor,
    lineHeight: 12 * 1.7,
  },
});

export default PhoneNumberInput;
