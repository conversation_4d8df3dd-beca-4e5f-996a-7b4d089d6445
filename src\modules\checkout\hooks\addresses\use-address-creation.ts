import {FormEvent, useState} from 'react';
import verifyAddressContent from '../../utils/address-verification';
import {useCheckoutStore} from '../../store/checkout-store';
import createAddressOnServerSide from '../../services/addresses/address-creation';
import {useQueryClient} from '@tanstack/react-query';
import useAddressSelection from '../../store/address-selection-store';

export default function useAddressCreation(useAddressIdInCheckout = false) {
  const queryClient = useQueryClient();

  const [warning, setWarning] = useState('');
  const [wrongInputs, setWrongInputs] = useState<string[]>([]);
  const [isLoading, setIsLoading] = useState(false);

  const [formData, setFormData] = useState({
    type: 'Home' as 'Home' | 'Work' | 'Other',
    address: '',
    city: '',
    state: '',
    zipCode: '',
    country: '',
  });

  const handleInputChange = (field: string, value: string) => {
    setFormData((prev) => ({...prev, [field]: value}));
  };

  const {country, city, countryOptionnalLabels} = useCheckoutStore();

  const selectAddressForCheckout = useAddressSelection(
    (store) => store.selectAddress,
  );

  function extractAddressInfo(): Record<string, string> | null {
    if (!country) return null;

    const verificationResult = verifyAddressContent(
      formData,
      countryOptionnalLabels,
      country,
      city,
    );

    if (!verificationResult.valid) {
      setWrongInputs(verificationResult.wrongInputsFinded);
      setWarning(verificationResult.warning);
      return null;
    } else {
      if (warning !== '') setWarning('');
      if (wrongInputs.length > 0) setWrongInputs([]);
    }

    return verificationResult.addressData;
  }

  async function createAddress(event?: FormEvent) {
    if (event) event.preventDefault();

    const addressData = extractAddressInfo();
    if (addressData) {
      setIsLoading(true);

      if (warning !== '') setWarning('');

      try {
        const res = await createAddressOnServerSide(addressData);
        let addressId = ''; //it will be used to return the address id for the checkout phase

        if (res.ok) {
          queryClient.invalidateQueries({
            queryKey: ['user-address'],
            exact: false,
          });

          if (!useAddressIdInCheckout)
            document.body.scrollIntoView({behavior: 'smooth'});
          else if (res.address) addressId = res.address.id;

          if (formData)
            setFormData({
              type: 'Home',
              address: '',
              city: '',
              state: '',
              zipCode: '',
              country: '',
            });
        } else {
          if (res.error === 'emailInvalid') setWarning('Invalid Email Format');
          else setWarning('Error while creating address');
        }

        setIsLoading(false);

        //returning address id to use in the chekout and select in case of checkout error
        if (useAddressIdInCheckout && addressId !== '') {
          selectAddressForCheckout(addressId);
          return addressId;
        }
      } catch {
        return '';
      }
    } else {
      const addressSection = document.getElementById('addressCreation');
      if (addressSection) addressSection.scrollIntoView({behavior: 'smooth'});
    }

    return '';
  }

  return {
    extractAddressInfo,
    wrongInputs,
    warning,
    createAddress,
    isLoading,
    formData,
    handleInputChange,
  };
}
